<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing token requested at fields for bank settings
        $settings = [
            'bank.ocb_token_requested_at' => null,
            'bank.mb_token_requested_at' => null,
            'bank.klb_token_requested_at' => null,
        ];

        foreach ($settings as $key => $value) {
            DB::table('settings')->updateOrInsert(
                ['group' => 'bank', 'name' => str_replace('bank.', '', $key)],
                ['group' => 'bank', 'name' => str_replace('bank.', '', $key), 'payload' => json_encode($value)]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $settingsToRemove = [
            'ocb_token_requested_at',
            'mb_token_requested_at',
            'klb_token_requested_at',
        ];

        foreach ($settingsToRemove as $setting) {
            DB::table('settings')->where('group', 'bank')->where('name', $setting)->delete();
        }
    }
};
