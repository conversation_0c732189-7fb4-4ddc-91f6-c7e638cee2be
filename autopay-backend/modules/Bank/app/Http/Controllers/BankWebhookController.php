<?php

namespace Modules\Bank\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Bank\Services\BankService;
use Modules\Core\Helpers\ResponseHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

/**
 * Bank Webhook Controller
 *
 * Handles webhook endpoints for all banks
 */
class BankWebhookController extends Controller
{
    /**
     * OCB Transaction Sync Webhook
     */
    public function ocbTransactionSync(Request $request): Response
    {
        try {
            // Handle empty payload (heartbeat)
            if (empty($request->all())) {
                return ResponseHelper::success('Pong', [
                    'code' => '00',
                ]);
            }

            Log::info('OCB transaction sync webhook received', [
                'payload' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            $bankTransactionId = $request->input('bankTransactionId');

            // Use OCB strategy to process transaction sync
            $ocbService = BankService::for('ocb');
            $result = $ocbService->syncTransaction($request->all());

            return ResponseHelper::success(
                $result['success'] ? 'Success' : 'Failed',
                [
                    'code' => $result['success'] ? '00' : '01',
                    'details' => $result['message'] ?? '',
                    'refTransactionId' => $bankTransactionId,
                ]
            );

        } catch (\Exception $e) {
            Log::error('OCB transaction sync webhook error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return ResponseHelper::error('Internal Error', [
                'code' => '01',
                'details' => 'Có lỗi xảy ra trong quá trình xử lý.',
                'refTransactionId' => $request->input('bankTransactionId'),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * MBBank Token Generate
     */
    public function mbTokenGenerate(Request $request): Response
    {
        try {
            $mbService = BankService::for('mb');
            $strategy = $mbService->getStrategy();
            $result = $strategy->generateToken();

            return ResponseHelper::success(data: [
                'access_token' => $result['access_token'],
                'token_type' => $result['token_type'],
                'expires_in' => $result['expires_in'],
            ]);

        } catch (\Exception $e) {
            Log::error('MBBank token generation error', [
                'error' => $e->getMessage()
            ]);

            return ResponseHelper::error('Unable to generate access token', [
                'error' => 'token_generation_failed',
                'error_description' => 'Unable to generate access token'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * MBBank Transaction Sync
     */
    public function mbTransactionSync(Request $request): Response
    {
        try {
            Log::info('MBBank transaction sync received', [
                'payload' => $request->all()
            ]);

            $mbService = BankService::for('mb');
            $result = $mbService->syncTransaction($request->all());

            return ResponseHelper::success($result['message'] ?? '', [
                'error' => !$result['success'],
                'errorReason' => $result['success'] ? '000' : '001',
                'toastMessage' => $result['message'] ?? '',
                'object' => [
                    'reftransactionid' => $request->json('transactionid') ?? '',
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('MBBank transaction sync error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return ResponseHelper::error('Internal server error', [
                'error' => true,
                'errorReason' => '999',
                'toastMessage' => 'Internal server error',
                'object' => [
                    'reftransactionid' => $request->json('transactionid') ?? '',
                ],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * MBBank Virtual Account Validation
     */
    public function mbVaAccount(Request $request): Response
    {
        try {
            $validated = $request->validate([
                'customerAcc' => 'required|min:8|max:19',
            ]);

            $mbService = BankService::for('mb');
            $strategy = $mbService->getStrategy();
            $result = $strategy->validateVirtualAccount($validated['customerAcc']);

            return ResponseHelper::success(data: $result);

        } catch (\Exception $e) {
            Log::error('MBBank VA account validation error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return ResponseHelper::error('Internal server error', [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'customerAcc' => $request->input('customerAcc'),
                    'customerName' => 'Error occurred',
                    'responseDesc' => 'Internal server error',
                    'responseCode' => '99',
                ],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * MBBank Virtual Account Transaction Sync
     */
    public function mbVaTransactionSync(Request $request): Response
    {
        try {
            $validated = $request->validate([
                'requestId' => 'required',
                'signature' => 'required',
                'data.referenceNumber' => 'required',
                'data.amount' => 'required',
                'data.customerAcc' => 'required|min:8|max:19',
                'data.transDate' => 'required',
                'data.billNumber' => 'required',
            ]);

            $mbService = BankService::for('mb');
            $strategy = $mbService->getStrategy();
            $result = $strategy->syncVirtualAccountTransaction($validated['data']);

            return ResponseHelper::success(data: $result);

        } catch (\Exception $e) {
            Log::error('MBBank VA transaction sync error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return ResponseHelper::error('Internal server error', [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'transactionId' => strtolower(Str::ulid()),
                    'responseCode' => '99',
                    'responseDesc' => 'Internal server error',
                ],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * KienLongBank Token Generate
     */
    public function klbTokenGenerate(Request $request): Response
    {
        return ResponseHelper::success(data: [
            'access_token' => 'a3e7ak92-333b-4078-8eeb-3a620051',
            'token_type' => 'bearer',
            'expires_in' => 59,
        ]);
    }

    /**
     * KienLongBank Transaction Sync
     */
    public function klbTransactionSync(Request $request): Response
    {
        try {
            Log::info('KLB transaction sync received', [
                'payload' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            $klbService = BankService::for('klb');
            $result = $klbService->syncTransaction($request->all());

            return ResponseHelper::success(data: $result);

        } catch (\Exception $e) {
            Log::error('KLB transaction sync error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return ResponseHelper::error('Internal server error', [
                'success' => false,
                'code' => '01',
                'message' => 'Internal server error',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
