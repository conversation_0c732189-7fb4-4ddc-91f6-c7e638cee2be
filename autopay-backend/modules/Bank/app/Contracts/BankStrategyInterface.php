<?php

namespace Modules\Bank\Contracts;

use Modules\Bank\DTOs\BankAccountValidationResult;
use Modules\Bank\DTOs\BankRegistrationResult;
use Modules\Bank\DTOs\TransactionHistoryResult;

/**
 * Bank Strategy Interface
 *
 * Defines the contract for all bank integration strategies.
 * Each bank implementation must implement this interface.
 */
interface BankStrategyInterface
{
    /**
     * Check if account number is valid and get account information
     *
     * @param string $accountNumber The account number to validate
     * @param array $additionalData Additional data required by specific bank (phone, ID card, etc.)
     * @return BankAccountValidationResult
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): BankAccountValidationResult;

    /**
     * Register account for banking services
     *
     * @param array $accountData Account registration data
     * @return BankRegistrationResult
     */
    public function registerAccount(array $accountData): BankRegistrationResult;

    /**
     * Get transaction history for an account
     *
     * @param string $accountNumber The account number
     * @param array $filters Filters for transaction history (date range, limit, etc.)
     * @return TransactionHistoryResult
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): TransactionHistoryResult;

    /**
     * Create virtual account (if supported by bank)
     *
     * @param array $virtualAccountData Data for virtual account creation
     * @return array
     */
    public function createVirtualAccount(array $virtualAccountData): array;

    /**
     * Delete/disable virtual account (if supported by bank)
     *
     * @param array $virtualAccountData Data for virtual account deletion
     * @return array
     */
    public function deleteVirtualAccount(array $virtualAccountData): array;

    /**
     * Get virtual account transactions
     *
     * @param array $filters Filters for VA transaction history
     * @return array
     */
    public function getVirtualAccountTransactions(array $filters = []): array;

    /**
     * Link account to banking services
     *
     * @param string $accountNumber The account number to link
     * @return array
     */
    public function linkAccount(string $accountNumber): array;

    /**
     * Verify linked account with OTP
     *
     * @param array $verificationData Verification data (sessionId, otp, accountNumber)
     * @return array
     */
    public function verifyLinkedAccount(array $verificationData): array;

    /**
     * Get account balance (if supported by bank)
     *
     * @param string $accountNumber The account number
     * @return float|null
     */
    public function getAccountBalance(string $accountNumber): ?float;

    /**
     * Check if bank supports specific feature
     *
     * @param string $feature Feature name (virtual_account, balance_check, transaction_history, etc.)
     * @return bool
     */
    public function supportsFeature(string $feature): bool;

    /**
     * Get bank configuration
     *
     * @return array
     */
    public function getBankConfig(): array;

    /**
     * Authenticate with bank and get access token
     *
     * @return string|null
     */
    public function authenticate(): ?string;

    /**
     * Refresh access token using refresh token
     *
     * @return string|null
     */
    public function refreshToken(): ?string;

    /**
     * Get valid access token (authenticate if needed)
     *
     * @return string|null
     */
    public function getAccessToken(): ?string;

    /**
     * Make authenticated HTTP request to bank API
     *
     * @param string $method HTTP method (GET, POST, etc.)
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @return array
     */
    public function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array;

    /**
     * Test bank connection
     *
     * @return array
     */
    public function testConnection(): array;

    /**
     * Clear all tokens for this bank
     *
     * @return void
     */
    public function clearTokens(): void;

    /**
     * Get token status for this bank
     *
     * @return array
     */
    public function getTokenStatus(): array;

    /**
     * Sync transaction with bank (optional method for banks that support it)
     *
     * @param array $transactionData Transaction data to sync
     * @return array
     */
    public function syncTransaction(array $transactionData): array;
}
