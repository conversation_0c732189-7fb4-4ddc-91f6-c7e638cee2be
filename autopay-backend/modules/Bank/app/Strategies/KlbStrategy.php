<?php

namespace Modules\Bank\Strategies;

use Modules\Bank\Contracts\BankStrategyInterface;
use Modules\Bank\DTOs\BankAccountValidationResult;
use Modules\Bank\DTOs\BankRegistrationResult;
use Modules\Bank\DTOs\TransactionHistoryResult;
use App\Settings\BankSettings;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

/**
 * KienLongBank Strategy Implementation
 *
 * Implements bank operations for KienLongBank using their API
 */
class KlbStrategy implements BankStrategyInterface
{
    protected array $config;
    protected BankSettings $bankSettings;
    protected string $baseUrl;
    protected string $clientId;
    protected string $secretKey;
    protected string $encryptKey;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->bankSettings = app(BankSettings::class);

        // Initialize KLB API configuration
        $apiConfig = $config['api_config'] ?? config('bank.supported_banks.klb.api_config', []);
        $this->baseUrl = $apiConfig['base_url'] ?? 'https://api.kienlongbank.co/pay';
        $this->clientId = $apiConfig['client_id'] ?? '';
        $this->secretKey = $apiConfig['secret_key'] ?? '';
        $this->encryptKey = $apiConfig['encrypt_key'] ?? '';
    }

    /**
     * Check if account number is valid and get account information
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): BankAccountValidationResult
    {
        try {
            $endpoint = $this->baseUrl . '/api/openBanking/v1/checkAccountNo';
            $timestamp = time() * 1000;

            $payload = json_encode([
                'accountNo' => $accountNumber,
            ]);

            $encryptedData = $this->encryptAES($payload, $this->encryptKey);
            $apiValidate = $this->hmacEncode($this->clientId . '|' . $timestamp . '|' . $encryptedData, $this->secretKey);

            $response = Http::asJson()->withHeaders([
                'x-api-client' => $this->clientId,
                'x-api-time' => $timestamp,
                'x-api-validate' => $apiValidate,
            ])->post($endpoint, [
                'data' => $encryptedData,
            ]);

            if ($response->failed()) {
                Log::warning('KLB account validation failed', [
                    'account_number' => $accountNumber,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                return BankAccountValidationResult::failed(
                    'Có lỗi xảy ra trong quá trình kết nối với ngân hàng KienLongBank.',
                    'API_ERROR'
                );
            }

            $responseData = $response->json();
            $decryptedData = null;

            if (data_get($responseData, 'data')) {
                $decryptedData = json_decode($this->decryptAES($responseData['data'], $this->encryptKey), true);
            }

            if (!$decryptedData) {
                return BankAccountValidationResult::failed(
                    'Không thể xác thực thông tin tài khoản KienLongBank.',
                    'INVALID_RESPONSE'
                );
            }

            // Extract account information from KLB response
            $accountName = $decryptedData['accountName'] ?? '';
            $bankName = 'KienLongBank';

            if (empty($accountName)) {
                return BankAccountValidationResult::failed(
                    'Tài khoản KienLongBank không tồn tại hoặc không hợp lệ.',
                    'ACCOUNT_NOT_FOUND'
                );
            }

            return BankAccountValidationResult::success(
                'Xác thực tài khoản KienLongBank thành công.',
                $accountName,
                $bankName,
                $decryptedData
            );

        } catch (Exception $e) {
            Log::error('KLB account validation error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
            ]);

            return BankAccountValidationResult::failed(
                'Có lỗi xảy ra trong quá trình kiểm tra tài khoản KienLongBank.',
                'INTERNAL_ERROR'
            );
        }
    }

    /**
     * Register account for banking services
     */
    public function registerAccount(array $accountData): BankRegistrationResult
    {
        Log::info('KienLongBank registration requested', [
            'account_data' => $accountData
        ]);

        return BankRegistrationResult::success(
            'Đăng ký tài khoản KienLongBank thành công.',
            $accountData,
            null
        );
    }

    /**
     * Get transaction history for an account
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): TransactionHistoryResult
    {
        Log::info('KienLongBank transaction history requested', [
            'account_number' => $accountNumber,
            'filters' => $filters
        ]);

        return TransactionHistoryResult::failed(
            'Tính năng lịch sử giao dịch KienLongBank không được hỗ trợ.',
            'NOT_SUPPORTED'
        );
    }

    /**
     * Create virtual account (if supported by bank)
     */
    public function createVirtualAccount(array $virtualAccountData): array
    {
        try {
            $endpoint = $this->baseUrl . '/api/payment/v1/virtualAccount/enable';
            $timestamp = time() * 1000;

            $payload = json_encode([
                'order' => $virtualAccountData['order'] ?? 1,
                'timeout' => $virtualAccountData['timeout'] ?? 0,
                'fixAmount' => $virtualAccountData['fix_amount'] ?? 0,
                'fixContent' => $virtualAccountData['fix_content'] ?? 'donate',
                'bankAccountNo' => $virtualAccountData['bank_account_no'] ?? '',
            ]);

            $encryptedData = $this->encryptAES($payload, $this->encryptKey);
            $apiValidate = $this->hmacEncode($this->clientId . '|' . $timestamp . '|' . $encryptedData, $this->secretKey);

            $response = Http::asJson()->withHeaders([
                'x-api-client' => $this->clientId,
                'x-api-time' => $timestamp,
                'x-api-validate' => $apiValidate,
            ])->post($endpoint, [
                'data' => $encryptedData,
            ]);

            if ($response->failed()) {
                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo KienLongBank.',
                    'error_code' => 'API_ERROR'
                ];
            }

            $responseData = $response->json();
            $decryptedData = null;

            if (data_get($responseData, 'data')) {
                $decryptedData = json_decode($this->decryptAES($responseData['data'], $this->encryptKey), true);
            }

            return [
                'success' => true,
                'message' => 'Tạo tài khoản ảo KienLongBank thành công.',
                'data' => $decryptedData
            ];

        } catch (Exception $e) {
            Log::error('KLB virtual account creation error', [
                'virtual_account_data' => $virtualAccountData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo KienLongBank.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Get account balance (if supported by bank)
     */
    public function getAccountBalance(string $accountNumber): ?float
    {
        // KienLongBank doesn't support balance check according to config
        return null;
    }

    /**
     * Check if bank supports specific feature
     */
    public function supportsFeature(string $feature): bool
    {
        $supportedFeatures = $this->config['features'] ?? [];
        return in_array($feature, $supportedFeatures);
    }

    /**
     * Get bank configuration
     */
    public function getBankConfig(): array
    {
        return $this->config;
    }

    /**
     * Sync transaction with bank (optional method for banks that support it)
     */
    public function syncTransaction(array $transactionData): array
    {
        try {
            $decryptedData = json_decode($this->decryptAES($transactionData['data'], $this->encryptKey), true);

            Log::info('KLB transaction sync processed', [
                'transaction_data' => $decryptedData
            ]);

            return [
                'success' => true,
                'code' => '00',
                'message' => 'Success',
                'data' => $decryptedData,
            ];

        } catch (Exception $e) {
            Log::error('KLB transaction sync error', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'code' => '01',
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch KienLongBank.',
                'error_code' => 'SYNC_ERROR'
            ];
        }
    }

    /**
     * Get virtual account transactions (if supported by bank)
     */
    public function getVirtualAccountTransactions(array $filters = []): array
    {
        try {
            $endpoint = $this->baseUrl . '/api/payment/v1/getTransaction';
            $timestamp = time() * 1000;

            $payload = json_encode([
                'order' => $filters['order'] ?? 1,
                'page' => $filters['page'] ?? 0,
                'size' => $filters['size'] ?? 500,
                'bankAccountNo' => $filters['bank_account_no'] ?? '',
                'fromDate' => $filters['from_date'] ?? date('Y-m-d 00:00:00'),
                'toDate' => $filters['to_date'] ?? date('Y-m-d 23:59:59'),
            ]);

            $encryptedData = $this->encryptAES($payload, $this->encryptKey);
            $apiValidate = $this->hmacEncode($this->clientId . '|' . $timestamp . '|' . $encryptedData, $this->secretKey);

            $response = Http::asJson()->withHeaders([
                'x-api-client' => $this->clientId,
                'x-api-time' => $timestamp,
                'x-api-validate' => $apiValidate,
            ])->post($endpoint, [
                'data' => $encryptedData,
            ]);

            if ($response->failed()) {
                Log::error('KLB virtual account transactions failed', [
                    'filters' => $filters,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo KienLongBank.',
                    'error_code' => 'API_ERROR',
                    'transactions' => []
                ];
            }

            $responseData = $response->json();
            $decryptedData = null;

            if (data_get($responseData, 'data')) {
                $decryptedData = json_decode($this->decryptAES($responseData['data'], $this->encryptKey), true);
            }

            Log::info('KLB virtual account transactions retrieved successfully', [
                'filters' => $filters,
                'response_data' => $decryptedData
            ]);

            return [
                'success' => true,
                'message' => 'Lấy giao dịch tài khoản ảo KienLongBank thành công.',
                'transactions' => $decryptedData['transactions'] ?? [],
                'data' => $decryptedData
            ];

        } catch (Exception $e) {
            Log::error('KLB virtual account transactions error', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo KienLongBank.',
                'error_code' => 'INTERNAL_ERROR',
                'transactions' => []
            ];
        }
    }

    /**
     * Link account to banking services
     */
    public function linkAccount(string $accountNumber): array
    {
        try {
            $endpoint = $this->baseUrl . '/api/openBanking/v1/linkAccount';
            $timestamp = time() * 1000;

            $payload = json_encode([
                'accountNo' => $accountNumber,
            ]);

            $encryptedData = $this->encryptAES($payload, $this->encryptKey);
            $apiValidate = $this->hmacEncode($this->clientId . '|' . $timestamp . '|' . $encryptedData, $this->secretKey);

            $response = Http::asJson()->withHeaders([
                'x-api-client' => $this->clientId,
                'x-api-time' => $timestamp,
                'x-api-validate' => $apiValidate,
            ])->post($endpoint, [
                'data' => $encryptedData,
            ]);

            if ($response->failed()) {
                Log::error('KLB account linking failed', [
                    'account_number' => $accountNumber,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình liên kết tài khoản KienLongBank.',
                    'error_code' => 'API_ERROR'
                ];
            }

            $responseData = $response->json();
            $decryptedData = null;

            if (data_get($responseData, 'data')) {
                $decryptedData = json_decode($this->decryptAES($responseData['data'], $this->encryptKey), true);
            }

            Log::info('KLB account linked successfully', [
                'account_number' => $accountNumber,
                'response_data' => $decryptedData
            ]);

            return [
                'success' => true,
                'message' => 'Liên kết tài khoản KienLongBank thành công.',
                'session_id' => $decryptedData['sessionId'] ?? null,
                'data' => $decryptedData
            ];

        } catch (Exception $e) {
            Log::error('KLB account linking error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình liên kết tài khoản KienLongBank.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Verify linked account with OTP
     */
    public function verifyLinkedAccount(array $verificationData): array
    {
        try {
            $endpoint = $this->baseUrl . '/api/openBanking/v1/linkAccount/verify';
            $timestamp = time() * 1000;

            $payload = json_encode([
                'sessionId' => $verificationData['session_id'] ?? '',
                'otp' => $verificationData['otp'] ?? '',
                'accountNo' => $verificationData['account_number'] ?? '',
            ]);

            $encryptedData = $this->encryptAES($payload, $this->encryptKey);
            $apiValidate = $this->hmacEncode($this->clientId . '|' . $timestamp . '|' . $encryptedData, $this->secretKey);

            $response = Http::asJson()->withHeaders([
                'x-api-client' => $this->clientId,
                'x-api-time' => $timestamp,
                'x-api-validate' => $apiValidate,
            ])->post($endpoint, [
                'data' => $encryptedData,
            ]);

            if ($response->failed()) {
                Log::error('KLB account verification failed', [
                    'verification_data' => $verificationData,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình xác thực tài khoản KienLongBank.',
                    'error_code' => 'API_ERROR'
                ];
            }

            $responseData = $response->json();
            $decryptedData = null;

            if (data_get($responseData, 'data')) {
                $decryptedData = json_decode($this->decryptAES($responseData['data'], $this->encryptKey), true);
            }

            Log::info('KLB account verified successfully', [
                'verification_data' => $verificationData,
                'response_data' => $decryptedData
            ]);

            return [
                'success' => true,
                'message' => 'Xác thực tài khoản KienLongBank thành công.',
                'data' => $decryptedData
            ];

        } catch (Exception $e) {
            Log::error('KLB account verification error', [
                'verification_data' => $verificationData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xác thực tài khoản KienLongBank.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }



    /**
     * Check if bank supports specific feature
     */
    public function supportsFeature(string $feature): bool
    {
        $supportedFeatures = $this->config['features'] ?? [];
        return in_array($feature, $supportedFeatures);
    }

    /**
     * Get bank configuration
     */
    public function getBankConfig(): array
    {
        return $this->config;
    }

    /**
     * Encrypt data using AES-256-CBC
     */
    protected function encryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $pri_key = hex2bin($key);
        $data_utf8 = mb_convert_encoding($data, 'UTF-8');
        $encrypted_data = openssl_encrypt(
            $data_utf8,
            'aes-256-cbc',
            $pri_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($encrypted_data);
    }

    /**
     * Decrypt data using AES-256-CBC
     */
    protected function decryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $key = hex2bin($key);

        return openssl_decrypt(
            base64_decode($data),
            'aes-256-cbc',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }

    /**
     * Generate HMAC signature
     */
    protected function hmacEncode(string $data, string $key): string
    {
        $hmac = hash_hmac('sha256', $data, $key, true);

        return bin2hex($hmac);
    }

    /**
     * Sync transaction with bank (optional method for banks that support it)
     */
    public function syncTransaction(array $transactionData): array
    {
        Log::info('KLB transaction sync requested', [
            'transaction_data' => $transactionData
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng đồng bộ giao dịch KienLongBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED'
        ];
    }

    /**
     * Authenticate with KienLongBank and get access token
     */
    public function authenticate(): ?string
    {
        try {
            $credentials = config('bank.supported_banks.klb.api_config');

            if (!$credentials['client_id'] || !$credentials['client_secret']) {
                Log::warning("Missing KLB credentials in config");
                return null;
            }

            // KLB authentication endpoint
            $endpoint = rtrim($credentials['base_url'], '/') . '/token-generate';

            $response = Http::asJson()->post($endpoint, [
                'client_id' => $credentials['client_id'],
                'client_secret' => $credentials['client_secret'],
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                $token = $responseData['access_token'] ?? null;

                if ($token) {
                    // Save token to BankSettings
                    $expiresIn = $responseData['expires_in'] ?? 3600; // Default 1 hour
                    $expiresAt = now()->addSeconds($expiresIn - 60)->toDateTimeString(); // Buffer of 1 minute
                    $this->bankSettings->setAccessToken('klb', $token, $expiresAt);

                    Log::info('KLB authentication successful');
                    return $token;
                }
            }

            Log::warning('KLB authentication failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;
        } catch (Exception $e) {
            Log::error('KLB authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(): ?string
    {
        // KLB doesn't support refresh tokens, re-authenticate instead
        Log::info('KLB refresh token requested, re-authenticating instead');
        return $this->authenticate();
    }

    /**
     * Get valid access token (authenticate if needed)
     */
    public function getAccessToken(): ?string
    {
        $token = $this->bankSettings->getAccessToken('klb');

        if (!$token || $this->bankSettings->isTokenExpired('klb')) {
            $token = $this->authenticate();
        }

        return $token;
    }

    /**
     * Make authenticated HTTP request to bank API
     */
    public function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        $token = $this->getAccessToken();

        if (!$token) {
            return [
                'success' => false,
                'message' => 'Unable to get valid access token',
                'error_code' => 'AUTH_FAILED'
            ];
        }

        // Get bank-specific API URL
        $apiUrl = config('bank.supported_banks.klb.api_config.base_url');
        if (!$apiUrl) {
            return [
                'success' => false,
                'message' => 'Bank API URL not configured',
                'error_code' => 'CONFIG_ERROR'
            ];
        }

        $url = rtrim($apiUrl, '/') . '/' . ltrim($endpoint, '/');

        try {
            $response = Http::withToken($token)
                ->timeout(30)
                ->{strtolower($method)}($url, $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status' => $response->status()
                ];
            }

            // If unauthorized, try to refresh token and retry once
            if ($response->status() === 401) {
                Log::info("KLB token expired, attempting to re-authenticate");

                $newToken = $this->authenticate();

                if ($newToken) {
                    $retryResponse = Http::withToken($newToken)
                        ->timeout(30)
                        ->{strtolower($method)}($url, $data);

                    if ($retryResponse->successful()) {
                        return [
                            'success' => true,
                            'data' => $retryResponse->json(),
                            'status' => $retryResponse->status()
                        ];
                    }
                }
            }

            return [
                'success' => false,
                'message' => 'API request failed',
                'error_code' => 'API_ERROR',
                'status' => $response->status(),
                'response' => $response->body()
            ];
        } catch (Exception $e) {
            Log::error("Error making authenticated request to KLB", [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint
            ]);

            return [
                'success' => false,
                'message' => 'Request failed due to network error',
                'error_code' => 'NETWORK_ERROR'
            ];
        }
    }

    /**
     * Test bank connection
     */
    public function testConnection(): array
    {
        try {
            $token = $this->getAccessToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Unable to authenticate with KLB',
                    'error_code' => 'AUTH_FAILED'
                ];
            }

            // Try to make a simple API call to test connection
            $result = $this->makeAuthenticatedRequest('GET', '/health');

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'KLB connection successful',
                    'token_expires_at' => $this->bankSettings->getTokenExpiration('klb')
                ];
            }

            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'KLB connection test failed: ' . $e->getMessage(),
                'error_code' => 'CONNECTION_FAILED'
            ];
        }
    }

    /**
     * Clear all tokens for KLB bank
     */
    public function clearTokens(): void
    {
        $this->bankSettings->clearTokens('klb');
        Log::info("Cleared all tokens for KLB bank");
    }

    /**
     * Get token status for KLB bank
     */
    public function getTokenStatus(): array
    {
        $token = $this->bankSettings->getAccessToken('klb');
        $expiresAt = $this->bankSettings->getTokenExpiration('klb');
        $requestedAt = $this->bankSettings->getTokenRequestedAt('klb');
        $isExpired = $this->bankSettings->isTokenExpired('klb');

        return [
            'has_token' => !empty($token),
            'expires_at' => $expiresAt,
            'requested_at' => $requestedAt,
            'is_expired' => $isExpired,
            'expires_in_minutes' => $expiresAt ? Carbon::parse($expiresAt)->diffInMinutes(now()) : null,
            'age_in_minutes' => $requestedAt ? Carbon::parse($requestedAt)->diffInMinutes(now()) : null
        ];
    }

    /**
     * Encrypt data using AES-256-CBC
     */
    protected function encryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $pri_key = hex2bin($key);
        $data_utf8 = mb_convert_encoding($data, 'UTF-8');
        $encrypted_data = openssl_encrypt(
            $data_utf8,
            'aes-256-cbc',
            $pri_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($encrypted_data);
    }

    /**
     * Decrypt data using AES-256-CBC
     */
    protected function decryptAES(string $data, string $key): string
    {
        $iv = hex2bin(substr($key, 0, 32));
        $key = hex2bin($key);

        return openssl_decrypt(
            base64_decode($data),
            'aes-256-cbc',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );
    }

    /**
     * Generate HMAC signature
     */
    protected function hmacEncode(string $data, string $key): string
    {
        $hmac = hash_hmac('sha256', $data, $key, true);

        return bin2hex($hmac);
    }
}
