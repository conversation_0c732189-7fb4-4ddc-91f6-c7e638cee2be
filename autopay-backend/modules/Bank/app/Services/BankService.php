<?php

namespace Modules\Bank\Services;

use Modules\Bank\Contracts\BankStrategyInterface;
use Modules\Bank\DTOs\BankAccountValidationResult;
use Modules\Bank\DTOs\BankRegistrationResult;
use Modules\Bank\DTOs\TransactionHistoryResult;
use Modules\Bank\Factories\BankStrategyFactory;
use Modules\Bank\Exceptions\UnsupportedBankException;
use Modules\Bank\Exceptions\BankStrategyNotFoundException;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Bank Service
 *
 * Main service class that uses Factory Pattern to create bank strategies
 * and provides a unified interface for bank operations.
 */
class BankService
{
    protected BankStrategyInterface $strategy;
    protected string $bankCode;

    /**
     * Constructor
     *
     * @param string $bankCode The bank code to initialize strategy for
     * @throws UnsupportedBankException
     * @throws BankStrategyNotFoundException
     */
    public function __construct(string $bankCode)
    {
        $this->bankCode = strtolower($bankCode);
        $this->strategy = BankStrategyFactory::create($this->bankCode);
    }

    /**
     * Create bank service instance for specific bank
     *
     * @param string $bankCode
     * @return static
     */
    public static function for(string $bankCode): static
    {
        return new static($bankCode);
    }

    /**
     * Check account number validity
     *
     * @param string $accountNumber
     * @param array $additionalData
     * @return BankAccountValidationResult
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): BankAccountValidationResult
    {
        try {
            Log::info("Checking account number for bank: {$this->bankCode}", [
                'account_number' => $accountNumber,
                'additional_data' => $additionalData
            ]);

            return $this->strategy->checkAccountNumber($accountNumber, $additionalData);
        } catch (Exception $e) {
            Log::error("Error checking account number for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'account_number' => $accountNumber
            ]);

            return BankAccountValidationResult::failed(
                'Có lỗi xảy ra trong quá trình kiểm tra tài khoản. Vui lòng thử lại sau.',
                'INTERNAL_ERROR'
            );
        }
    }

    /**
     * Register account for banking services
     *
     * @param array $accountData
     * @return BankRegistrationResult
     */
    public function registerAccount(array $accountData): BankRegistrationResult
    {
        try {
            Log::info("Registering account for bank: {$this->bankCode}", [
                'account_data' => $accountData
            ]);

            return $this->strategy->registerAccount($accountData);
        } catch (Exception $e) {
            Log::error("Error registering account for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'account_data' => $accountData
            ]);

            return BankRegistrationResult::failed(
                'Có lỗi xảy ra trong quá trình đăng ký tài khoản. Vui lòng thử lại sau.',
                'INTERNAL_ERROR'
            );
        }
    }

    /**
     * Get transaction history
     *
     * @param string $accountNumber
     * @param array $filters
     * @return TransactionHistoryResult
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): TransactionHistoryResult
    {
        try {
            Log::info("Getting transaction history for bank: {$this->bankCode}", [
                'account_number' => $accountNumber,
                'filters' => $filters
            ]);

            return $this->strategy->getTransactionHistory($accountNumber, $filters);
        } catch (Exception $e) {
            Log::error("Error getting transaction history for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'account_number' => $accountNumber
            ]);

            return TransactionHistoryResult::failed(
                'Có lỗi xảy ra trong quá trình lấy lịch sử giao dịch. Vui lòng thử lại sau.',
                'INTERNAL_ERROR'
            );
        }
    }

    /**
     * Create virtual account
     *
     * @param array $virtualAccountData
     * @return array
     */
    public function createVirtualAccount(array $virtualAccountData): array
    {
        try {
            if (!$this->strategy->supportsFeature('virtual_account')) {
                return [
                    'success' => false,
                    'message' => 'Ngân hàng này không hỗ trợ tạo tài khoản ảo.',
                    'error_code' => 'FEATURE_NOT_SUPPORTED'
                ];
            }

            Log::info("Creating virtual account for bank: {$this->bankCode}", [
                'virtual_account_data' => $virtualAccountData
            ]);

            return $this->strategy->createVirtualAccount($virtualAccountData);
        } catch (Exception $e) {
            Log::error("Error creating virtual account for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'virtual_account_data' => $virtualAccountData
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Delete virtual account
     *
     * @param array $virtualAccountData
     * @return array
     */
    public function deleteVirtualAccount(array $virtualAccountData): array
    {
        try {
            if (!$this->strategy->supportsFeature('virtual_account')) {
                return [
                    'success' => false,
                    'message' => 'Ngân hàng này không hỗ trợ tài khoản ảo.',
                    'error_code' => 'FEATURE_NOT_SUPPORTED'
                ];
            }

            Log::info("Deleting virtual account for bank: {$this->bankCode}", [
                'virtual_account_data' => $virtualAccountData
            ]);

            return $this->strategy->deleteVirtualAccount($virtualAccountData);
        } catch (Exception $e) {
            Log::error("Error deleting virtual account for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'virtual_account_data' => $virtualAccountData
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xóa tài khoản ảo. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Get virtual account transactions
     *
     * @param array $filters
     * @return array
     */
    public function getVirtualAccountTransactions(array $filters = []): array
    {
        try {
            if (!$this->strategy->supportsFeature('virtual_account')) {
                return [
                    'success' => false,
                    'message' => 'Ngân hàng này không hỗ trợ tài khoản ảo.',
                    'error_code' => 'FEATURE_NOT_SUPPORTED',
                    'transactions' => []
                ];
            }

            Log::info("Getting virtual account transactions for bank: {$this->bankCode}", [
                'filters' => $filters
            ]);

            return $this->strategy->getVirtualAccountTransactions($filters);
        } catch (Exception $e) {
            Log::error("Error getting virtual account transactions for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'filters' => $filters
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR',
                'transactions' => []
            ];
        }
    }

    /**
     * Link account to banking services
     *
     * @param string $accountNumber
     * @return array
     */
    public function linkAccount(string $accountNumber): array
    {
        try {
            Log::info("Linking account for bank: {$this->bankCode}", [
                'account_number' => $accountNumber
            ]);

            return $this->strategy->linkAccount($accountNumber);
        } catch (Exception $e) {
            Log::error("Error linking account for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'account_number' => $accountNumber
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình liên kết tài khoản. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Verify linked account with OTP
     *
     * @param array $verificationData
     * @return array
     */
    public function verifyLinkedAccount(array $verificationData): array
    {
        try {
            Log::info("Verifying linked account for bank: {$this->bankCode}", [
                'verification_data' => $verificationData
            ]);

            return $this->strategy->verifyLinkedAccount($verificationData);
        } catch (Exception $e) {
            Log::error("Error verifying linked account for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'verification_data' => $verificationData
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xác thực tài khoản. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }

    /**
     * Get account balance
     *
     * @param string $accountNumber
     * @return float|null
     */
    public function getAccountBalance(string $accountNumber): ?float
    {
        try {
            if (!$this->strategy->supportsFeature('balance_check')) {
                return null;
            }

            Log::info("Getting account balance for bank: {$this->bankCode}", [
                'account_number' => $accountNumber
            ]);

            return $this->strategy->getAccountBalance($accountNumber);
        } catch (Exception $e) {
            Log::error("Error getting account balance for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'account_number' => $accountNumber
            ]);

            return null;
        }
    }

    /**
     * Check if bank supports specific feature
     *
     * @param string $feature
     * @return bool
     */
    public function supportsFeature(string $feature): bool
    {
        return $this->strategy->supportsFeature($feature);
    }

    /**
     * Get bank configuration
     *
     * @return array
     */
    public function getBankConfig(): array
    {
        return $this->strategy->getBankConfig();
    }

    /**
     * Get current bank code
     *
     * @return string
     */
    public function getBankCode(): string
    {
        return $this->bankCode;
    }

    /**
     * Get the underlying strategy instance
     *
     * @return BankStrategyInterface
     */
    public function getStrategy(): BankStrategyInterface
    {
        return $this->strategy;
    }

    /**
     * Get supported banks list
     *
     * @return array
     */
    public static function getSupportedBanks(): array
    {
        return BankStrategyFactory::getSupportedBanks();
    }

    /**
     * Check if bank is supported
     *
     * @param string $bankCode
     * @return bool
     */
    public static function isSupported(string $bankCode): bool
    {
        return BankStrategyFactory::isSupported($bankCode);
    }

    /**
     * Sync transaction with bank (if supported)
     *
     * @param array $transactionData
     * @return array
     */
    public function syncTransaction(array $transactionData): array
    {
        try {
            // Check if strategy has syncTransaction method
            if (!method_exists($this->strategy, 'syncTransaction')) {
                return [
                    'success' => false,
                    'message' => 'Ngân hàng này không hỗ trợ đồng bộ giao dịch.',
                    'error_code' => 'FEATURE_NOT_SUPPORTED'
                ];
            }

            Log::info("Syncing transaction for bank: {$this->bankCode}", [
                'transaction_data' => $transactionData
            ]);

            return $this->strategy->syncTransaction($transactionData);
        } catch (Exception $e) {
            Log::error("Error syncing transaction for bank: {$this->bankCode}", [
                'error' => $e->getMessage(),
                'transaction_data' => $transactionData
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch. Vui lòng thử lại sau.',
                'error_code' => 'INTERNAL_ERROR'
            ];
        }
    }
}
