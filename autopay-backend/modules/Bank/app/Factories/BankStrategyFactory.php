<?php

namespace Modules\Bank\Factories;

use Modules\Bank\Contracts\BankStrategyInterface;
use Modules\Bank\Exceptions\BankStrategyNotFoundException;
use Modules\Bank\Exceptions\UnsupportedBankException;

/**
 * Bank Strategy Factory
 *
 * Factory class is responsible for creating bank strategy instances
 * based on bank code using the Factory Pattern.
 */
class BankStrategyFactory
{
    /**
     * Create a bank strategy instance based on bank code
     *
     * @param  string  $bankCode  The bank code (e.g., 'ocb', 'mb', 'klb')
     *
     * @throws UnsupportedBankException
     * @throws BankStrategyNotFoundException
     */
    public static function create(string $bankCode): BankStrategyInterface
    {
        $bankCode = strtolower($bankCode);

        // Get bank configuration
        $bankConfig = config("bank.supported_banks.{$bankCode}");

        if (! $bankConfig) {
            throw new UnsupportedBankException("Bank '{$bankCode}' is not supported.");
        }

        if (! isset($bankConfig['strategy_class'])) {
            throw new BankStrategyNotFoundException("Strategy class not configured for bank '{$bankCode}'.");
        }

        $strategyClass = $bankConfig['strategy_class'];

        if (! class_exists($strategyClass)) {
            throw new BankStrategyNotFoundException("Strategy class '{$strategyClass}' not found for bank '{$bankCode}'.");
        }

        $strategy = new $strategyClass($bankConfig);

        if (! $strategy instanceof BankStrategyInterface) {
            throw new BankStrategyNotFoundException("Strategy class '{$strategyClass}' must implement BankStrategyInterface.");
        }

        return $strategy;
    }

    /**
     * Get a list of supported banks
     */
    public static function getSupportedBanks(): array
    {
        return array_keys(config('bank.supported_banks', []));
    }

    /**
     * Check if a bank is supported
     */
    public static function isSupported(string $bankCode): bool
    {
        return in_array(strtolower($bankCode), self::getSupportedBanks());
    }

    /**
     * Get bank configuration
     */
    public static function getBankConfig(string $bankCode): ?array
    {
        return config('bank.supported_banks.'.strtolower($bankCode));
    }

    /**
     * Get all banks configuration
     */
    public static function getAllBanksConfig(): array
    {
        return config('bank.supported_banks', []);
    }
}
