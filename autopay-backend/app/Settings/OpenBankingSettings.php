<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class OpenBankingSettings extends Settings
{
    // OCB Bank Settings - using array to store complex data
    public array $ocb;

    public static function group(): string
    {
        return 'open_banking';
    }

    /**
     * Get OCB access token
     */
    public function getOcbAccessToken(): ?string
    {
        return $this->ocb['access_token'] ?? null;
    }

    /**
     * Set OCB access token with expiration
     */
    public function setOcbAccessToken(string $token, ?string $expiresAt = null): void
    {
        $this->ocb = array_merge($this->ocb ?? [], [
            'access_token' => $token,
            'expired_at' => $expiresAt,
            'token_requested_at' => now()->toISOString(),
        ]);
        $this->save();
    }

    /**
     * Check if OCB token is expired
     */
    public function isOcbTokenExpired(): bool
    {
        $expiresAt = $this->ocb['expired_at'] ?? null;
        if (!$expiresAt) {
            return true;
        }

        return now()->isAfter($expiresAt);
    }

    /**
     * Clear OCB tokens
     */
    public function clearOcbTokens(): void
    {
        $this->ocb = [];
        $this->save();
    }

    /**
     * Get OCB token status
     */
    public function getOcbTokenStatus(): array
    {
        $token = $this->getOcbAccessToken();
        $expiresAt = $this->ocb['expired_at'] ?? null;
        $requestedAt = $this->ocb['token_requested_at'] ?? null;
        $isExpired = $this->isOcbTokenExpired();

        return [
            'has_token' => !empty($token),
            'expires_at' => $expiresAt,
            'requested_at' => $requestedAt,
            'is_expired' => $isExpired,
            'expires_in_minutes' => $expiresAt ? now()->diffInMinutes($expiresAt) : null,
            'age_in_minutes' => $requestedAt ? now()->diffInMinutes($requestedAt) : null
        ];
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults(): void
    {
        if (!isset($this->ocb)) {
            $this->ocb = [];
            $this->save();
        }
    }
}
